'use client'

import { useState, useMemo, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  Merge, 
  AlertTriangle, 
  CheckCircle2, 
  Users, 
  Calendar,
  Database as DatabaseIcon
} from 'lucide-react';
import ExerciseSelectionTable from './ExerciseSelectionTable';
import MasterSelectionPanel from './MasterSelectionPanel';
import ConsolidationPreview from './ConsolidationPreview';
import ConsolidationExecutor from './ConsolidationExecutor';

// Enhanced exercise type with consolidation data
export type ConsolidationExercise = Database['public']['Tables']['exercise_movements']['Row'] & {
  categories: Database['public']['Tables']['exercise_categories']['Row'][];
  images: Database['public']['Tables']['exercise_images']['Row'][];
  videos: Database['public']['Tables']['exercise_videos']['Row'][];
  recordCount: number;
  lastUsed?: string;
  completenessScore: number;
  nameQualityScore: number;
};

export type ConsolidationStep = 'select' | 'choose-master' | 'preview' | 'execute' | 'complete';

interface ConsolidationClientProps {
  exercises: (Database['public']['Tables']['exercise_movements']['Row'] & {
    categories: Database['public']['Tables']['exercise_categories']['Row'][];
    images: Database['public']['Tables']['exercise_images']['Row'][];
    videos: Database['public']['Tables']['exercise_videos']['Row'][];
    hasRecords: boolean;
  })[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  searchParams: Record<string, string>;
  recordCounts: Map<string, number>;
}

export default function ConsolidationClient({
  exercises,
  totalCount,
  currentPage,
  pageSize,
  totalPages,
  searchParams,
  recordCounts
}: ConsolidationClientProps) {
  const router = useRouter();
  const currentSearchParams = useSearchParams();
  const supabase = createClientComponentClient<Database>();
  
  // State management
  const [currentStep, setCurrentStep] = useState<ConsolidationStep>('select');
  const [selectedExerciseIds, setSelectedExerciseIds] = useState<Set<string>>(new Set());
  const [masterId, setMasterId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [consolidationResult, setConsolidationResult] = useState<any>(null);

  // Current search term from URL (like working exercises page)
  const currentSearchTerm = searchParams.search || '';

  // URL update function (copied from working exercises page)
  const updateURL = useCallback((updates: Record<string, string | null>) => {
    const params = new URLSearchParams(currentSearchParams.toString());
    
    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === '' || value === 'all') {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });

    const newURL = `?${params.toString()}`;
    router.push(newURL);
  }, [currentSearchParams, router]);

  // Search handler (copied from working exercises page)  
  const handleSearchChange = useCallback((searchTerm: string) => {
    updateURL({ search: searchTerm });
  }, [updateURL]);

  // Calculate completeness score for an exercise
  const calculateCompletenessScore = useCallback((exercise: any): number => {
    let score = 0;
    if (exercise.description && exercise.description.trim()) score += 20;
    if (exercise.categories && exercise.categories.length > 0) score += 30;
    if (exercise.images && exercise.images.length > 0) score += 25;
    if (exercise.videos && exercise.videos.length > 0) score += 25;
    return score;
  }, []);

  // Calculate name quality score
  const calculateNameQualityScore = useCallback((name: string): number => {
    let score = 50; // Base score
    
    // Prefer shorter names
    if (name.length < 20) score += 10;
    if (name.length < 15) score += 5;
    
    // Penalize equipment in name if it could be in categories
    const equipmentWords = ['barbell', 'dumbbell', 'kettlebell', 'machine', 'cable'];
    if (equipmentWords.some(word => name.toLowerCase().includes(word))) score -= 10;
    
    // Prefer standard case
    if (name === name.toLowerCase()) score -= 5;
    if (name.includes('_')) score -= 5;
    
    return Math.max(0, score);
  }, []);

  // Enhanced exercises with consolidation data  
  const enhancedExercises = useMemo<ConsolidationExercise[]>(() => {
    return exercises.map(exercise => {
      // Get record count from recordCounts map
      const recordCount = recordCounts.get(exercise.id) || 0;

      const enhanced = {
        ...exercise,
        recordCount, // Use actual count for display
        completenessScore: 0,
        nameQualityScore: 0
      };

      // Calculate scores
      enhanced.completenessScore = calculateCompletenessScore(enhanced);
      enhanced.nameQualityScore = calculateNameQualityScore(exercise.exercise_name);

      return enhanced;
    });
  }, [exercises, recordCounts, calculateCompletenessScore, calculateNameQualityScore]);

  // No client-side filtering needed - server already filtered based on search
  const filteredExercises = enhancedExercises;

  // Selected exercises data
  const selectedExercises = useMemo(() => {
    return enhancedExercises.filter(ex => selectedExerciseIds.has(ex.id));
  }, [enhancedExercises, selectedExerciseIds]);

  // Recommended master based on consolidation rules
  const recommendedMasterId = useMemo(() => {
    if (selectedExercises.length === 0) return null;
    
    // Calculate total score for each exercise
    const scored = selectedExercises.map(exercise => ({
      id: exercise.id,
      score: (
        exercise.recordCount * 2 + // Records are most important
        exercise.completenessScore +
        exercise.nameQualityScore
      )
    }));
    
    // Sort by score descending
    scored.sort((a, b) => b.score - a.score);
    
    return scored[0]?.id || null;
  }, [selectedExercises]);

  // Step navigation functions
  const handleNextStep = () => {
    switch (currentStep) {
      case 'select':
        if (selectedExerciseIds.size < 2) {
          setError('Please select at least 2 exercises to consolidate');
          return;
        }
        setError(null);
        setCurrentStep('choose-master');
        break;
      case 'choose-master':
        if (!masterId) {
          setError('Please select a master exercise');
          return;
        }
        setError(null);
        setCurrentStep('preview');
        break;
      case 'preview':
        setCurrentStep('execute');
        break;
    }
  };

  const handlePreviousStep = () => {
    switch (currentStep) {
      case 'choose-master':
        setCurrentStep('select');
        break;
      case 'preview':
        setCurrentStep('choose-master');
        break;
      case 'execute':
        setCurrentStep('preview');
        break;
    }
    setError(null);
  };

  const handleReset = () => {
    setCurrentStep('select');
    setSelectedExerciseIds(new Set());
    setMasterId(null);
    setError(null);
    setConsolidationResult(null);
  };

  // Exercise selection handlers
  const handleExerciseSelect = (exerciseId: string, selected: boolean) => {
    const newSelected = new Set(selectedExerciseIds);
    if (selected) {
      newSelected.add(exerciseId);
    } else {
      newSelected.delete(exerciseId);
      // If we deselected the master, clear master selection
      if (exerciseId === masterId) {
        setMasterId(null);
      }
    }
    setSelectedExerciseIds(newSelected);
  };

  const handleSelectAll = (exerciseIds: string[]) => {
    setSelectedExerciseIds(new Set(exerciseIds));
  };

  const handleClearSelection = () => {
    setSelectedExerciseIds(new Set());
    setMasterId(null);
  };

  // Master selection handler
  const handleMasterSelect = (exerciseId: string) => {
    setMasterId(exerciseId);
  };

  // Consolidation execution handler
  const handleExecuteConsolidation = async (consolidationData: any) => {
    try {
      setIsLoading(true);
      setError(null);

      // Here we'll implement the actual consolidation logic
      // For now, just simulate success
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setConsolidationResult({
        success: true,
        masterId,
        mergedExerciseIds: Array.from(selectedExerciseIds).filter(id => id !== masterId),
        recordsMigrated: selectedExercises.reduce((sum, ex) => sum + ex.recordCount, 0),
        timestamp: new Date().toISOString()
      });
      
      setCurrentStep('complete');
    } catch (error) {
      console.error('Consolidation error:', error);
      setError(error instanceof Error ? error.message : 'Failed to consolidate exercises');
    } finally {
      setIsLoading(false);
    }
  };

  // Step indicator component
  const StepIndicator = () => {
    const steps = [
      { key: 'select', label: 'Select Exercises', icon: Search },
      { key: 'choose-master', label: 'Choose Master', icon: Users },
      { key: 'preview', label: 'Preview Changes', icon: AlertTriangle },
      { key: 'execute', label: 'Execute', icon: Merge },
      { key: 'complete', label: 'Complete', icon: CheckCircle2 }
    ];

    const currentIndex = steps.findIndex(step => step.key === currentStep);

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isActive = step.key === currentStep;
          const isCompleted = index < currentIndex;
          const isDisabled = index > currentIndex;

          return (
            <div key={step.key} className="flex items-center">
              <div className={`
                flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                ${isActive ? 'bg-blue-600 border-blue-600 text-white' : ''}
                ${isCompleted ? 'bg-green-600 border-green-600 text-white' : ''}
                ${isDisabled ? 'bg-gray-100 border-gray-300 text-gray-400' : ''}
                ${!isActive && !isCompleted && !isDisabled ? 'bg-white border-gray-300 text-gray-600' : ''}
              `}>
                <Icon className="h-5 w-5" />
              </div>
              <span className={`ml-2 text-sm font-medium ${
                isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
              }`}>
                {step.label}
              </span>
              {index < steps.length - 1 && (
                <div className={`ml-4 mr-4 h-0.5 w-8 ${
                  isCompleted ? 'bg-green-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div>
      <StepIndicator />

      {/* Error Alert */}
      {error && (
        <Alert className="mb-6 border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Step Content */}
      {currentStep === 'select' && (
        <ExerciseSelectionTable
          exercises={filteredExercises}
          selectedExerciseIds={selectedExerciseIds}
          searchTerm={currentSearchTerm}
          onSearchChange={handleSearchChange}
          onExerciseSelect={handleExerciseSelect}
          onSelectAll={handleSelectAll}
          onClearSelection={handleClearSelection}
        />
      )}

      {currentStep === 'choose-master' && (
        <MasterSelectionPanel
          exercises={selectedExercises}
          masterId={masterId}
          recommendedMasterId={recommendedMasterId}
          onMasterSelect={handleMasterSelect}
        />
      )}

      {currentStep === 'preview' && masterId && (
        <ConsolidationPreview
          exercises={selectedExercises}
          masterId={masterId}
        />
      )}

      {currentStep === 'execute' && masterId && (
        <ConsolidationExecutor
          exercises={selectedExercises}
          masterId={masterId}
          isLoading={isLoading}
          onExecute={handleExecuteConsolidation}
        />
      )}

      {currentStep === 'complete' && consolidationResult && (
        <Card className="p-8 text-center">
          <CheckCircle2 className="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-green-800 mb-2">
            Consolidation Complete!
          </h3>
          <p className="text-green-600 mb-6">
            Successfully merged {consolidationResult.mergedExerciseIds.length} exercises and 
            migrated {consolidationResult.recordsMigrated} records.
          </p>
          <div className="flex gap-4 justify-center">
            <Button onClick={handleReset}>
              Start New Consolidation
            </Button>
            <Button variant="outline" asChild>
              <a href="/exercises">Return to Exercises</a>
            </Button>
          </div>
        </Card>
      )}

      {/* Navigation Controls */}
      {currentStep !== 'complete' && (
        <Card className="p-4 mt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Badge variant="outline">
                Step {['select', 'choose-master', 'preview', 'execute'].indexOf(currentStep) + 1} of 4
              </Badge>
              {selectedExerciseIds.size > 0 && (
                <span className="text-sm text-gray-600">
                  {selectedExerciseIds.size} exercises selected
                </span>
              )}
            </div>
            
            <div className="flex gap-2">
              {currentStep !== 'select' && (
                <Button 
                  variant="outline" 
                  onClick={handlePreviousStep}
                  disabled={isLoading}
                >
                  Previous
                </Button>
              )}
              
              {currentStep !== 'execute' && (
                <Button 
                  onClick={handleNextStep}
                  disabled={isLoading}
                >
                  {currentStep === 'preview' ? 'Proceed to Execute' : 'Next'}
                </Button>
              )}
              
              <Button 
                variant="outline" 
                onClick={handleReset}
                disabled={isLoading}
              >
                Reset
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}