import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';
import { Suspense } from 'react';
import ConsolidationClient from '@/components/exercises/ConsolidationClient';
import { Card } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Database as DatabaseIcon } from 'lucide-react';
import Link from 'next/link';

// Server-side data fetching - using same pattern as working exercises page
async function fetchConsolidationData() {
  const supabase = createServerComponentClient<Database>({ cookies });
  
  try {
    // Step 1: Get exercises with simple query (like your working page)
    const { data: exercises, error: exerciseError, count } = await supabase
      .from('exercise_movements')
      .select('*', { count: 'exact' })
      .order('exercise_name')
      .range(0, 3000); // Same range as your working page

    if (exerciseError) {
      console.error('Error fetching exercises:', exerciseError);
      throw exerciseError;
    }

    console.log(`Found ${exercises?.length || 0} exercises out of ${count} total`);

    if (!exercises || exercises.length === 0) {
      return {
        exercises: [],
        categories: [],
        images: [],
        videos: [],
        recordCounts: new Map(),
        totalExercises: count || 0
      };
    }

    // Step 2: Get related data separately (same as working page)
    const exerciseIds = exercises.map(ex => ex.id);

    const [categoriesResponse, imagesResponse, videosResponse, recordsResponse] = await Promise.all([
      // Get categories
      supabase
        .from('exercise_categories')
        .select('*')
        .in('exercise_id', exerciseIds),
      
      // Get images
      supabase
        .from('exercise_images')
        .select('*')
        .in('exercise_id', exerciseIds),
      
      // Get videos
      supabase
        .from('exercise_videos')
        .select('*')
        .in('exercise_id', exerciseIds),
      
      // Get exercises with records (debug version)
      supabase
        .from('exercise_records')
        .select('exercise_id')
        .in('exercise_id', exerciseIds)
        .not('exercise_id', 'is', null)
    ]);

    const categories = categoriesResponse.data || [];
    const images = imagesResponse.data || [];
    const videos = videosResponse.data || [];
    const exerciseRecords = new Set((recordsResponse.data || []).map(r => r.exercise_id));

    console.log(`Related data: ${categories.length} categories, ${images.length} images, ${videos.length} videos, ${exerciseRecords.size} with records`);

    // Step 3: Combine data (same as working page)
    const combinedExercises = exercises.map(exercise => ({
      ...exercise,
      categories: categories.filter(cat => cat.exercise_id === exercise.id),
      images: images.filter(img => img.exercise_id === exercise.id),
      videos: videos.filter(vid => vid.exercise_id === exercise.id),
      hasRecords: exerciseRecords.has(exercise.id), // Same boolean logic as working page
      recordCount: 0 // We'll calculate this separately for consolidation display
    }));

    // Calculate actual record counts for consolidation interface
    const recordCounts = new Map<string, number>();
    (recordsResponse.data || []).forEach(record => {
      const count = recordCounts.get(record.exercise_id) || 0;
      recordCounts.set(record.exercise_id, count + 1);
    });

    return {
      exercises: combinedExercises,
      categories,
      images,
      videos,
      recordCounts,
      totalExercises: count || 0
    };

  } catch (error) {
    console.error('Error in fetchConsolidationData:', error);
    return {
      exercises: [],
      categories: [],
      images: [],
      videos: [],
      recordCounts: new Map(),
      totalExercises: 0
    };
  }
}

// Loading component
function ConsolidationLoading() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-10">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="h-32 bg-gray-200 rounded mb-6"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );
}

// Main page component
export default async function ExerciseConsolidationPage({ searchParams }: ExercisesPageProps) {
  const data = await fetchConsolidationData(searchParams);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-10">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link 
              href="/exercises" 
              className="flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Exercises
            </Link>
          </div>
          
          <div className="flex items-center gap-3 mb-2">
            <DatabaseIcon className="h-6 w-6 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Exercise Consolidation</h1>
          </div>
          
          <p className="text-gray-600">
            Merge duplicate or similar exercises to improve data quality and user experience.
          </p>
          
          {/* Show current search info like working page */}
          {searchParams.search && (
            <p className="text-sm text-gray-500 mt-2">
              Found {data.totalCount} exercises matching "{searchParams.search}"
            </p>
          )}
        </div>

        {/* Warning Alert */}
        <Alert className="mb-6 border-orange-200 bg-orange-50">
          <AlertDescription className="text-orange-800">
            <strong>Important:</strong> Exercise consolidation will permanently merge exercise records. 
            Ensure you have a recent backup before proceeding. This action affects user data and cannot be easily undone.
          </AlertDescription>
        </Alert>

        {/* Stats Card */}
        <Card className="p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{data.totalCount}</div>
              <div className="text-sm text-gray-600">
                {searchParams.search ? 'Search Results' : 'Total Exercises'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Array.from(data.recordCounts?.values() || []).reduce((sum, count) => sum + count, 0)}
              </div>
              <div className="text-sm text-gray-600">Total Records</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{data.exercises.length}</div>
              <div className="text-sm text-gray-600">Loaded Exercises</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {data.exercises.reduce((sum, ex) => sum + (ex.images?.length || 0) + (ex.videos?.length || 0), 0)}
              </div>
              <div className="text-sm text-gray-600">Media Files</div>
            </div>
          </div>
        </Card>

        {/* Main Consolidation Interface */}
        <Suspense fallback={<ConsolidationLoading />}>
          <ConsolidationClient 
            exercises={data.exercises}
            totalCount={data.totalCount}
            currentPage={data.currentPage}
            pageSize={data.pageSize}
            totalPages={data.totalPages}
            searchParams={searchParams}
            recordCounts={data.recordCounts || new Map()}
          />
        </Suspense>
      </div>
    </div>
  );
}